# Playwright + Midscene.js 测试框架

一个集成了 [midscene.js](https://midscenejs.com/) AI 功能的 Playwright 自动化测试框架。通过 AI 驱动的方式，让 Web 自动化测试更加智能和简单。

## ✨ 特性

- 🤖 **AI 驱动测试** - 使用自然语言描述进行页面交互
- 🎯 **智能元素定位** - 无需编写复杂的选择器
- 📊 **数据提取** - 智能提取页面结构化数据
- 🔍 **内容验证** - AI 驱动的断言和验证
- 📈 **丰富报告** - 集成 midscene 可视化测试报告
- 🌐 **多浏览器支持** - Chrome、Firefox、Safari、Edge
- 📱 **移动端测试** - 支持移动设备模拟

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装项目依赖
npm install

# 安装 Playwright 浏览器
npx playwright install
```

### 2. 配置 AI 模型

`.env` 文件已创建并配置了阿里云千问模型服务：

```bash
# 阿里云千问配置 (已配置)
OPENAI_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
OPENAI_API_KEY="sk-2ba8822ac5684a84a0e801ae256076ac"
MIDSCENE_MODEL_NAME="qwen-vl-max-latest"
MIDSCENE_USE_QWEN_VL=1

# 也可以使用其他AI服务
# OpenAI 官方服务
# OPENAI_API_KEY=sk-your-openai-api-key-here
# OPENAI_BASE_URL=https://api.openai.com/v1

# 其他服务
# ANTHROPIC_API_KEY=your-anthropic-api-key
# GOOGLE_AI_API_KEY=your-google-ai-api-key
```

### 3. 运行示例测试

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npx playwright test test/e2e/example.spec.ts

# 以可视化模式运行
npm run test:ui

# 以有头模式运行（显示浏览器）
npm run test:headed

# 调试模式
npm run test:debug
```

## 📖 使用指南

### 基本用法

```typescript
import { test, expect } from './fixture';

test('AI 驱动的搜索测试', async ({ 
  aiInput, 
  aiTap, 
  aiQuery, 
  aiAssert 
}) => {
  // 访问网站
  await page.goto('https://example.com');
  
  // 使用自然语言进行交互
  await aiInput('搜索关键词', '搜索框');
  await aiTap('搜索按钮');
  
  // 提取结构化数据
  const results = await aiQuery<Array<{title: string, price: string}>>(
    '获取搜索结果的标题和价格'
  );
  
  // AI 驱动的断言
  await aiAssert('页面显示了搜索结果列表');
  
  // 传统断言
  expect(results.length).toBeGreaterThan(0);
});
```

### 可用的 AI 功能

#### 基础交互
- `aiInput(text, description)` - 智能输入文本
- `aiTap(description)` - 智能点击元素
- `aiHover(description)` - 智能悬停
- `aiScroll(options, description)` - 智能滚动
- `aiKeyboardPress(key, description)` - 键盘操作

#### 数据查询
- `aiQuery<T>(query)` - 提取结构化数据
- `aiString(query)` - 提取字符串
- `aiNumber(query)` - 提取数字
- `aiBoolean(query)` - 提取布尔值
- `aiAsk(question)` - 询问页面信息

#### 验证和等待
- `aiAssert(assertion)` - AI 断言
- `aiWaitFor(condition, options)` - 智能等待
- `aiLocate(description)` - 定位元素

### 高级用法

#### 自定义配置

```typescript
// test/e2e/fixture.ts
export const test = base.extend<PlayWrightAiFixtureType>(
  PlaywrightAiFixture({
    waitForNetworkIdleTimeout: 3000, // 网络空闲等待时间
    // 其他配置选项...
  }),
);
```

#### 使用 PageAgent

```typescript
test('使用 PageAgent API', async ({ agentForPage, page }) => {
  const agent = await agentForPage(page);
  
  // 记录截图
  await agent.logScreenshot();
  
  // 获取日志内容
  const logContent = agent._unstableLogContent();
  console.log(logContent);
});
```

## 📁 项目结构

```
ai-agent-auto1/
├── test/
│   ├── e2e/                    # E2E 测试文件
│   │   ├── fixture.ts          # Midscene fixture 配置
│   │   └── example.spec.ts     # 示例测试用例
│   └── utils/                  # 测试工具函数
│       └── test-helpers.ts
├── test-results/               # 测试结果输出
├── playwright.config.ts        # Playwright 配置
├── tsconfig.json              # TypeScript 配置
├── package.json               # 项目依赖
└── README.md                  # 项目文档
```

## 🎯 测试示例

### eBay 搜索测试

```typescript
test('eBay 耳机搜索', async ({ aiInput, aiTap, aiQuery, aiAssert }) => {
  await page.goto('https://www.ebay.com');
  
  await aiInput('Wireless Headphones', '搜索框');
  await aiTap('搜索按钮');
  
  const products = await aiQuery<Array<{title: string, price: string}>>(
    '获取前5个商品的标题和价格'
  );
  
  await aiAssert('页面左侧有筛选选项');
  expect(products.length).toBeGreaterThan(0);
});
```

### GitHub 仓库测试

```typescript
test('GitHub 仓库信息', async ({ aiInput, aiTap, aiQuery }) => {
  await page.goto('https://github.com');
  
  await aiInput('microsoft/playwright', '搜索框');
  await aiTap('搜索按钮');
  await aiTap('第一个搜索结果');
  
  const repoInfo = await aiQuery<{stars: string, forks: string}>>(
    '获取仓库的星标数和分叉数'
  );
  
  expect(repoInfo.stars).toBeTruthy();
});
```

## 📊 测试报告

运行测试后，可以查看以下报告：

1. **Playwright HTML 报告**
   ```bash
   npm run test:report
   ```

2. **Midscene 可视化报告**
   - 测试完成后控制台会显示报告文件路径
   - 用浏览器打开 `./midscene_run/report/xxx.html`

## 🔧 配置说明

### Playwright 配置

主要配置项在 `playwright.config.ts` 中：

- `timeout`: 测试超时时间（90秒）
- `reporter`: 集成了 midscene 报告器
- `projects`: 支持多浏览器测试
- `use`: 全局设置（截图、视频、追踪）

### Midscene 配置

在 `fixture.ts` 中配置 midscene 选项：

- `waitForNetworkIdleTimeout`: 网络空闲等待时间
- 其他AI模型相关配置

### 环境变量

当前已配置的环境变量：

```bash
# AI 模型配置 - 阿里云千问
OPENAI_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
OPENAI_API_KEY="sk-2ba8822ac5684a84a0e801ae256076ac"
MIDSCENE_MODEL_NAME="qwen-vl-max-latest"
MIDSCENE_USE_QWEN_VL=1

# Midscene 配置
MIDSCENE_TIMEOUT=30000
MIDSCENE_NETWORK_IDLE_TIMEOUT=2000
MIDSCENE_SCREENSHOT_QUALITY=80
MIDSCENE_DEBUG=false

# 测试配置
TEST_BASE_URL=https://www.example.com
BROWSER_HEADLESS=true
MAX_WORKERS=4
TEST_TIMEOUT=90000

# 报告配置
VIDEO_MODE=on-failure
TRACE_MODE=on-failure
```

## 🛠️ 开发指南

### 添加新测试

1. 在 `test/e2e/` 目录下创建新的 `.spec.ts` 文件
2. 从 `./fixture` 导入 `test` 和 `expect`
3. 使用 AI 功能编写测试用例

### 自定义工具函数

在 `test/utils/test-helpers.ts` 中添加通用的工具函数。

### 调试技巧

1. **使用调试模式**：`npm run test:debug`
2. **查看 AI 交互日志**：检查控制台输出
3. **截图和视频**：失败时自动生成
4. **Playwright Inspector**：`--debug` 标志启用

## 📚 参考资料

- [Midscene.js 官方文档](https://midscenejs.com/)
- [Playwright 官方文档](https://playwright.dev/)
- [AI 模型配置指南](https://midscenejs.com/zh/choose-a-model)
- [API 参考](https://midscenejs.com/zh/api)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个测试框架！

## �� 许可证

ISC License 
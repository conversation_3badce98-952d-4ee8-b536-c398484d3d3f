<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试执行中心 - AI智能测试平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">测试执行中心</h1>
                <p class="text-gray-600">监控和管理测试执行任务</p>
            </div>
            <div class="flex space-x-3">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-200">
                    <i class="fas fa-play mr-2"></i>执行测试
                </button>
                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                    <i class="fas fa-calendar mr-2"></i>定时任务
                </button>
            </div>
        </div>

        <!-- 执行状态概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-spinner fa-spin text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">执行中</p>
                        <p class="text-2xl font-bold text-gray-900">3</p>
                        <p class="text-xs text-blue-600">2个队列等待</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">今日成功</p>
                        <p class="text-2xl font-bold text-gray-900">127</p>
                        <p class="text-xs text-green-600">+15% 比昨天</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">今日失败</p>
                        <p class="text-2xl font-bold text-gray-900">8</p>
                        <p class="text-xs text-red-600">-3% 比昨天</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <i class="fas fa-clock text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">平均时长</p>
                        <p class="text-2xl font-bold text-gray-900">2.3s</p>
                        <p class="text-xs text-green-600">-0.5s 比昨天</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：当前执行任务 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 正在执行的任务 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">正在执行</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="border border-yellow-200 bg-yellow-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-spinner fa-spin text-yellow-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-medium text-gray-900">电商平台 - 用户登录测试套件</h4>
                                        <p class="text-sm text-gray-600">包含 15 个测试用例</p>
                                    </div>
                                </div>
                                <button class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-stop-circle"></i>
                                </button>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>进度: 8/15 完成</span>
                                    <span>执行时间: 00:02:34</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 53%"></div>
                                </div>
                                <div class="text-xs text-gray-600">
                                    当前: 验证用户密码重置功能
                                </div>
                            </div>
                        </div>

                        <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-spinner fa-spin text-blue-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-medium text-gray-900">管理后台 - 权限管理测试</h4>
                                        <p class="text-sm text-gray-600">包含 8 个测试用例</p>
                                    </div>
                                </div>
                                <button class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-stop-circle"></i>
                                </button>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>进度: 3/8 完成</span>
                                    <span>执行时间: 00:01:12</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-500" style="width: 37%"></div>
                                </div>
                                <div class="text-xs text-gray-600">
                                    当前: 测试角色权限分配
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近完成的任务 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">最近完成</h3>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">查看全部</a>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-900">购物车功能测试</h4>
                                    <p class="text-sm text-gray-600">12个用例全部通过 • 5分钟前</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-medium text-green-600">100% 通过</span>
                                <p class="text-xs text-gray-500">执行时间: 00:03:24</p>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-times text-red-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-900">支付流程测试</h4>
                                    <p class="text-sm text-gray-600">6个用例，2个失败 • 12分钟前</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-medium text-red-600">67% 通过</span>
                                <p class="text-xs text-gray-500">执行时间: 00:04:56</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：执行队列和控制面板 -->
            <div class="space-y-6">
                <!-- 执行队列 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">执行队列</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-xs font-medium">1</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">搜索功能测试</p>
                                    <p class="text-xs text-gray-600">电商平台 • 9个用例</p>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-red-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-xs font-medium">2</span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">数据导出测试</p>
                                    <p class="text-xs text-gray-600">管理后台 • 5个用例</p>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-red-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <button class="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 transition duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            添加到队列
                        </button>
                    </div>
                </div>

                <!-- 快速执行 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">快速执行</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择项目</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>电商平台</option>
                                <option>管理后台</option>
                                <option>移动应用</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">测试套件</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>全部测试用例</option>
                                <option>冒烟测试</option>
                                <option>回归测试</option>
                                <option>核心功能测试</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">执行环境</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>测试环境</option>
                                <option>预发布环境</option>
                                <option>生产环境</option>
                            </select>
                        </div>
                        <button class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition duration-200">
                            <i class="fas fa-play mr-2"></i>
                            立即执行
                        </button>
                    </div>
                </div>

                <!-- 执行设置 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">执行设置</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">并行执行</label>
                            <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">失败时停止</label>
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">生成报告</label>
                            <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">邮件通知</label>
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

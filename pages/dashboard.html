<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - AI智能测试平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 顶部欢迎区域 -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">欢迎回来，张工程师</h1>
                    <p class="text-gray-600">今天是 2024年12月15日，让我们开始新的测试工作</p>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                        <i class="fas fa-plus mr-2"></i>新建测试
                    </button>
                    <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-200">
                        <i class="fas fa-magic mr-2"></i>AI生成
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <i class="fas fa-list-check text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总测试用例</p>
                        <p class="text-2xl font-bold text-gray-900">1,247</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up"></i> +12% 本周
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">通过率</p>
                        <p class="text-2xl font-bold text-gray-900">94.2%</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up"></i> +2.1% 本周
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">执行时间</p>
                        <p class="text-2xl font-bold text-gray-900">2.3h</p>
                        <p class="text-xs text-red-600">
                            <i class="fas fa-arrow-down"></i> -15% 本周
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100">
                        <i class="fas fa-robot text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">AI生成用例</p>
                        <p class="text-2xl font-bold text-gray-900">342</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up"></i> +28% 本周
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：最近测试执行 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">最近测试执行</h3>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">查看全部</a>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="font-medium text-gray-900">用户登录流程测试</p>
                                        <p class="text-sm text-gray-600">电商平台项目 • 5分钟前</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        通过
                                    </span>
                                    <p class="text-sm text-gray-500 mt-1">执行时间: 2.3s</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-times text-red-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="font-medium text-gray-900">购物车功能测试</p>
                                        <p class="text-sm text-gray-600">电商平台项目 • 12分钟前</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        失败
                                    </span>
                                    <p class="text-sm text-gray-500 mt-1">执行时间: 4.7s</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-spinner fa-spin text-yellow-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="font-medium text-gray-900">支付流程测试</p>
                                        <p class="text-sm text-gray-600">电商平台项目 • 正在执行</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        执行中
                                    </span>
                                    <p class="text-sm text-gray-500 mt-1">已执行: 1.2s</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：快速操作和项目状态 -->
            <div class="space-y-6">
                <!-- 快速操作 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">快速操作</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button class="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                            <div class="flex items-center">
                                <i class="fas fa-magic text-purple-600 mr-3"></i>
                                <span class="font-medium">AI生成测试用例</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        
                        <button class="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                            <div class="flex items-center">
                                <i class="fas fa-play text-green-600 mr-3"></i>
                                <span class="font-medium">执行测试套件</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                        
                        <button class="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                            <div class="flex items-center">
                                <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                                <span class="font-medium">查看测试报告</span>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </button>
                    </div>
                </div>

                <!-- 项目状态 -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">活跃项目</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium text-gray-900">电商平台</p>
                                <p class="text-sm text-gray-600">156个测试用例</p>
                            </div>
                            <div class="text-right">
                                <div class="w-16 h-2 bg-gray-200 rounded-full">
                                    <div class="w-12 h-2 bg-green-500 rounded-full"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">75%</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium text-gray-900">管理后台</p>
                                <p class="text-sm text-gray-600">89个测试用例</p>
                            </div>
                            <div class="text-right">
                                <div class="w-16 h-2 bg-gray-200 rounded-full">
                                    <div class="w-14 h-2 bg-blue-500 rounded-full"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">87%</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-medium text-gray-900">移动应用</p>
                                <p class="text-sm text-gray-600">234个测试用例</p>
                            </div>
                            <div class="text-right">
                                <div class="w-16 h-2 bg-gray-200 rounded-full">
                                    <div class="w-8 h-2 bg-yellow-500 rounded-full"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">50%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

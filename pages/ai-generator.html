<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI测试生成器 - AI智能测试平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">AI测试生成器</h1>
            <p class="text-gray-600">使用自然语言描述您的测试需求，AI将自动生成完整的测试用例</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：输入区域 -->
            <div class="space-y-6">
                <!-- 项目选择 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">项目设置</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择项目</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>电商平台项目</option>
                                <option>管理后台项目</option>
                                <option>移动应用项目</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">测试环境</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>开发环境</option>
                                <option>测试环境</option>
                                <option>预发布环境</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 自然语言输入 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-magic text-purple-600 mr-2"></i>
                        描述您的测试需求
                    </h3>
                    <div class="space-y-4">
                        <textarea 
                            class="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                            placeholder="请用自然语言描述您想要测试的功能，例如：
• 测试用户登录功能，包括正确和错误的用户名密码
• 验证购物车添加商品、修改数量、删除商品的完整流程
• 检查支付页面的表单验证和支付流程
• 测试搜索功能的关键词搜索和筛选功能"></textarea>
                        
                        <div class="flex items-center space-x-4">
                            <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                <i class="fas fa-magic mr-2"></i>
                                生成测试用例
                            </button>
                            <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition duration-200">
                                <i class="fas fa-microphone mr-2"></i>
                                语音输入
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 高级选项 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">高级选项</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">包含边界值测试</label>
                            <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">生成负面测试用例</label>
                            <input type="checkbox" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">包含性能测试</label>
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm font-medium text-gray-700">生成数据驱动测试</label>
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </div>
                    </div>
                </div>

                <!-- 模板选择 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快速模板</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <button class="p-3 text-left border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition duration-200">
                            <i class="fas fa-sign-in-alt text-blue-600 mb-2"></i>
                            <p class="font-medium text-sm">用户登录</p>
                        </button>
                        <button class="p-3 text-left border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition duration-200">
                            <i class="fas fa-shopping-cart text-green-600 mb-2"></i>
                            <p class="font-medium text-sm">购物流程</p>
                        </button>
                        <button class="p-3 text-left border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition duration-200">
                            <i class="fas fa-credit-card text-purple-600 mb-2"></i>
                            <p class="font-medium text-sm">支付功能</p>
                        </button>
                        <button class="p-3 text-left border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition duration-200">
                            <i class="fas fa-search text-orange-600 mb-2"></i>
                            <p class="font-medium text-sm">搜索功能</p>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧：生成结果 -->
            <div class="space-y-6">
                <!-- 生成状态 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">生成结果</h3>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm text-green-600">AI正在分析...</span>
                        </div>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="mb-6">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>生成进度</span>
                            <span>75%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-500" style="width: 75%"></div>
                        </div>
                    </div>

                    <!-- 生成的测试用例预览 -->
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">用户登录 - 正常流程</h4>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">已生成</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-3">验证用户使用正确的用户名和密码登录系统</p>
                            <div class="bg-gray-50 rounded p-3">
                                <code class="text-xs text-gray-700">
                                    1. 打开登录页面<br>
                                    2. 输入有效用户名: "<EMAIL>"<br>
                                    3. 输入有效密码: "password123"<br>
                                    4. 点击登录按钮<br>
                                    5. 验证跳转到仪表板页面
                                </code>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">用户登录 - 错误密码</h4>
                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">生成中</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-3">验证用户输入错误密码时的错误处理</p>
                            <div class="bg-gray-50 rounded p-3">
                                <div class="flex items-center">
                                    <i class="fas fa-spinner fa-spin text-blue-600 mr-2"></i>
                                    <span class="text-sm text-gray-600">AI正在生成测试步骤...</span>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4 opacity-50">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">用户登录 - 空字段验证</h4>
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">等待中</span>
                            </div>
                            <p class="text-sm text-gray-600">验证必填字段的验证逻辑</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">操作选项</h3>
                    <div class="space-y-3">
                        <button class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                            <i class="fas fa-save mr-2"></i>
                            保存所有测试用例
                        </button>
                        <button class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
                            <i class="fas fa-play mr-2"></i>
                            立即执行测试
                        </button>
                        <button class="w-full flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            编辑测试用例
                        </button>
                        <button class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-200">
                            <i class="fas fa-download mr-2"></i>
                            导出为Playwright代码
                        </button>
                    </div>
                </div>

                <!-- AI建议 -->
                <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border border-purple-200">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-lightbulb text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-purple-900 mb-2">AI建议</h4>
                            <p class="text-sm text-purple-700">
                                基于您的描述，建议添加以下测试场景：
                            </p>
                            <ul class="text-sm text-purple-700 mt-2 space-y-1">
                                <li>• 会话超时后的重新登录</li>
                                <li>• 多次登录失败的账户锁定</li>
                                <li>• 记住密码功能验证</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

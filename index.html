<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能测试平台 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .prototype-container {
            height: calc(100vh - 120px);
        }
        .nav-item {
            transition: all 0.3s ease;
        }
        .nav-item:hover {
            background-color: #f3f4f6;
        }
        .nav-item.active {
            background-color: #3b82f6;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部标题栏 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-robot text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">AI智能测试平台</h1>
                    <span class="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">原型展示</span>
                </div>
                <div class="text-sm text-gray-500">
                    企业级UI自动化测试智能体平台
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex gap-6">
            <!-- 左侧导航 -->
            <div class="w-64 bg-white rounded-lg shadow-sm p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">页面导航</h3>
                <nav class="space-y-2">
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/login.html')">
                        <i class="fas fa-sign-in-alt mr-3"></i>
                        登录页面
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/dashboard.html')">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        仪表板
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/ai-generator.html')">
                        <i class="fas fa-magic mr-3"></i>
                        AI测试生成器
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/test-cases.html')">
                        <i class="fas fa-list-check mr-3"></i>
                        测试用例管理
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/test-execution.html')">
                        <i class="fas fa-play-circle mr-3"></i>
                        测试执行中心
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/test-reports.html')">
                        <i class="fas fa-chart-line mr-3"></i>
                        测试报告分析
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/projects.html')">
                        <i class="fas fa-folder-open mr-3"></i>
                        项目管理
                    </a>
                    <a href="#" class="nav-item flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700" 
                       onclick="loadPage('pages/settings.html')">
                        <i class="fas fa-cog mr-3"></i>
                        系统设置
                    </a>
                </nav>
            </div>

            <!-- 右侧内容展示区域 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-4 border-b">
                        <h2 id="current-page-title" class="text-lg font-semibold text-gray-900">欢迎使用AI智能测试平台</h2>
                        <p class="text-sm text-gray-600 mt-1">请选择左侧导航查看不同页面的原型设计</p>
                    </div>
                    <div class="prototype-container">
                        <iframe id="prototype-frame" 
                                src="pages/dashboard.html" 
                                class="w-full h-full border-0"
                                frameborder="0">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function loadPage(pageUrl) {
            const frame = document.getElementById('prototype-frame');
            const title = document.getElementById('current-page-title');
            
            // 更新iframe源
            frame.src = pageUrl;
            
            // 更新标题
            const pageNames = {
                'pages/login.html': '登录页面',
                'pages/dashboard.html': '仪表板',
                'pages/ai-generator.html': 'AI测试生成器',
                'pages/test-cases.html': '测试用例管理',
                'pages/test-execution.html': '测试执行中心',
                'pages/test-reports.html': '测试报告分析',
                'pages/projects.html': '项目管理',
                'pages/settings.html': '系统设置'
            };
            
            title.textContent = pageNames[pageUrl] || '页面展示';
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // 默认加载仪表板
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('[onclick="loadPage(\'pages/dashboard.html\')"]').classList.add('active');
        });
    </script>
</body>
</html>

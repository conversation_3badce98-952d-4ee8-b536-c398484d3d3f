# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 测试结果
test-results/
playwright-report/
midscene_run/
screenshots/
videos/
traces/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
logs/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
.cache/

# 构建输出
dist/
build/
coverage/

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/ 
import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * Playwright 配置文件 - 集成 midscene.js
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './test/e2e',
  /* 测试超时时间 */
  timeout: 90 * 1000,
  /* 全局超时时间 */
  globalTimeout: 600000,
  /* 期望超时时间 */
  expect: {
    timeout: 30000,
  },
  /* 在CI上并行运行测试 */
  fullyParallel: true,
  /* CI上失败重试 */
  retries: process.env.CI ? 2 : 0,
  /* CI上选择worker数量 */
  workers: process.env.CI ? 1 : undefined,
  /* 报告器配置 - 集成 midscene 报告器 */
  reporter: [
    ['list'],
    ['html', { outputFolder: 'test-results/html-report' }],
    ['@midscene/web/playwright-reporter', { 
      type: 'merged' // 多个测试用例生成一个报告，可选值: 'separate'
    }]
  ],
  /* 全局设置 */
  use: {
    /* 基础URL */
    baseURL: 'http://localhost:3000',
    /* 操作超时时间 */
    actionTimeout: 30000,
    /* 导航超时时间 */
    navigationTimeout: 30000,
    /* 追踪配置 */
    trace: 'on-first-retry',
    /* 截图配置 */
    screenshot: 'only-on-failure',
    /* 视频录制 */
    video: 'retain-on-failure',
  },

  /* 配置项目 */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },

    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },

    /* 移动端浏览器测试 */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },

    // /* Microsoft Edge测试 */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },

    /* Google Chrome测试 */
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* 输出目录 */
  outputDir: 'test-results/',

  /* Web服务器配置 (可选) */
  // webServer: {
  //   command: 'npm run start',
  //   url: 'http://127.0.0.1:3000',
  //   reuseExistingServer: !process.env.CI,
  // },
}); 
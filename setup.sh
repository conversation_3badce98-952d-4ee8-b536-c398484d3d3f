#!/bin/bash

# Playwright + Midscene.js 测试框架安装脚本

echo "🚀 开始安装 Playwright + Midscene.js 测试框架..."

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 请先安装 Node.js (推荐版本 18+)"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'.' -f1 | cut -d'v' -f2)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "⚠️  警告: 建议使用 Node.js 18+ 版本，当前版本: $(node -v)"
fi

echo "✅ Node.js 版本: $(node -v)"

# 安装依赖
echo "📦 安装 npm 依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ npm install 失败"
    exit 1
fi

# 安装 Playwright 浏览器
echo "🌐 安装 Playwright 浏览器..."
npx playwright install

if [ $? -ne 0 ]; then
    echo "❌ Playwright 浏览器安装失败"
    exit 1
fi

# 创建环境配置文件
if [ ! -f .env ]; then
    echo "⚙️  创建环境配置文件..."
    cat > .env << EOF
# AI 模型配置
# ==================

# OpenAI 配置 (推荐)
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# 其他AI服务配置 (可选)
# ANTHROPIC_API_KEY=your-anthropic-api-key
# GOOGLE_AI_API_KEY=your-google-ai-api-key

# 测试配置
# ========
TEST_BASE_URL=https://www.example.com
BROWSER_HEADLESS=true
MAX_WORKERS=4
TEST_TIMEOUT=90000

# 报告配置
VIDEO_MODE=on-failure
TRACE_MODE=on-failure
EOF
    echo "✅ 已创建 .env 文件，请配置你的 API Key"
else
    echo "⚠️  .env 文件已存在，跳过创建"
fi

# 创建测试结果目录
mkdir -p test-results
mkdir -p test-results/html-report

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 编辑 .env 文件，配置你的 OpenAI API Key"
echo "2. 运行测试: npm test"
echo "3. 查看 UI 模式: npm run test:ui"
echo ""
echo "�� 更多信息请查看 README.md" 
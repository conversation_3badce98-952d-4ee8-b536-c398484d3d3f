import { test as base } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { PlaywrightAiFixture } from '@midscene/web/playwright';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * 扩展 Playwright 测试实例，集成 midscene.js AI 功能
 * 
 * 使用方法:
 * import { test } from './fixture';
 * 
 * test('测试用例', async ({ ai, aiTap, aiInput, page }) => {
 *   // 使用 AI 功能进行测试
 * });
 */
export const test = base.extend<PlayWrightAiFixtureType>(
  PlaywrightAiFixture({
    // 等待网络空闲的超时时间，单位毫秒
    // 设置为 0 则禁用网络空闲等待
    waitForNetworkIdleTimeout: 2000,
    
    // 可选配置项（根据需要添加）
    // screenshotOptions: {
    //   quality: 80,
    //   type: 'jpeg'
    // }
  }),
);

export { expect } from '@playwright/test'; 
import { test, expect } from './fixture';

/**
 * 示例测试：eBay 搜索功能测试
 * 演示如何使用 midscene.js 的 AI 功能进行自动化测试
 */
test.describe('eBay 搜索功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 设置视口大小
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // 访问 eBay 首页
    await page.goto('https://www.ebay.com');
    await page.waitForLoadState('networkidle');
  });

  test('搜索耳机并验证结果', async ({
    ai,
    aiQuery,
    aiAssert,
    aiInput,
    aiTap,
    aiScroll,
    aiWaitFor,
    page
  }) => {
    // 使用 aiInput 输入搜索关键词
    await aiInput('Wireless Headphones', '搜索框');

    // 使用 aiTap 点击搜索按钮
    await aiTap('搜索按钮');

    // 等待搜索结果加载
    await aiWaitFor('搜索结果列表已加载', { timeoutMs: 10000 });

    // 使用 aiScroll 滚动查看更多结果
    await aiScroll(
      {
        direction: 'down',
        scrollType: 'untilBottom',
      },
      '搜索结果列表',
    );

    // 使用 aiQuery 获取商品信息
    const items = await aiQuery<Array<{ title: string; price: string }>>(
      '获取搜索结果中前5个商品的标题和价格',
    );

    console.log('搜索到的耳机商品:', items);
    
    // 验证搜索结果
    expect(items).toBeTruthy();
    expect(items!.length).toBeGreaterThan(0);

    // 使用 aiAssert 验证页面功能
    await aiAssert('页面左侧有分类筛选功能');
    await aiAssert('页面顶部有排序选项');
  });

  test('使用AI询问页面信息', async ({
    aiAsk,
    aiString,
    aiNumber,
    aiBoolean,
    page
  }) => {
    // 访问某个商品页面进行测试
    await page.goto('https://www.ebay.com/sch/i.html?_nkw=headphones');
    await page.waitForLoadState('networkidle');

    // 使用 aiAsk 询问页面信息
    const pageInfo = await aiAsk('这个页面主要展示什么内容？');
    console.log('页面信息:', pageInfo);

    // 使用 aiString 提取文本信息
    const searchKeyword = await aiString('页面当前的搜索关键词是什么？');
    console.log('搜索关键词:', searchKeyword);

    // 使用 aiNumber 提取数字信息
    const resultCount = await aiNumber('搜索结果大约有多少个？');
    console.log('搜索结果数量:', resultCount);

    // 使用 aiBoolean 判断页面状态
    const hasSortOptions = await aiBoolean('页面是否有排序选项？');
    console.log('是否有排序选项:', hasSortOptions);

    // 验证结果
    expect(searchKeyword).toContain('headphones');
    expect(resultCount).toBeGreaterThan(0);
    expect(hasSortOptions).toBe(true);
  });
});

/**
 * 示例测试：GitHub 网站测试
 * 演示更复杂的交互场景
 */
test.describe('GitHub 网站测试', () => {
  test('搜索仓库并查看详情', async ({
    ai,
    aiInput,
    aiTap,
    aiWaitFor,
    aiQuery,
    aiAssert,
    page
  }) => {
    // 访问 GitHub
    await page.goto('https://github.com');
    await page.waitForLoadState('networkidle');

    // 搜索 Playwright 仓库
    await aiInput('microsoft/playwright', '搜索框');
    await aiTap('搜索按钮');

    // 等待搜索结果
    await aiWaitFor('搜索结果页面加载完成', { timeoutMs: 10000 });

    // 点击第一个仓库
    await aiTap('第一个搜索结果');

    // 等待仓库页面加载
    await aiWaitFor('仓库详情页面加载完成', { timeoutMs: 10000 });

    // 获取仓库信息
    const repoInfo = await aiQuery<{
      stars: string;
      forks: string;
      language: string;
    }>('获取仓库的星标数、分叉数和主要编程语言');

    console.log('仓库信息:', repoInfo);

    // 验证页面元素
    await aiAssert('页面显示了代码文件列表');
    await aiAssert('页面显示了仓库的README文档');
    await aiAssert('页面右侧显示了仓库统计信息');

    // 验证获取的数据
    expect(repoInfo).toBeTruthy();
    expect(repoInfo!.language).toContain('TypeScript');
  });
});

/**
 * 示例测试：表单交互测试
 * 演示表单填写和提交功能
 */
test.describe('表单交互测试', () => {
  test('填写联系表单', async ({
    aiInput,
    aiTap,
    aiSelect,
    aiAssert,
    page
  }) => {
    // 访问一个包含表单的测试页面
    await page.goto('https://demoqa.com/text-box');
    await page.waitForLoadState('networkidle');

    // 填写表单字段
    await aiInput('张三', '全名输入框');
    await aiInput('<EMAIL>', '邮箱输入框');
    await aiInput('北京市朝阳区', '当前地址输入框');
    await aiInput('上海市浦东新区', '永久地址输入框');

    // 提交表单
    await aiTap('提交按钮');

    // 验证提交结果
    await aiAssert('表单提交成功，显示了提交的信息');
  });
}); 
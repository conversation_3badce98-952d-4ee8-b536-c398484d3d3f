import { test, expect } from './fixture';

/**
 * 快速开始测试 - 验证 midscene.js 集成是否正常工作
 */
test.describe('快速开始验证', () => {
  test('验证 AI 功能基本工作', async ({ 
    page,
    aiInput, 
    aiTap, 
    aiQuery,
    aiAssert,
    aiString,
    aiBoolean
  }) => {
    // 访问一个简单的测试页面
    await page.goto('https://demoqa.com/text-box');
    await page.waitForLoadState('networkidle');

    // 验证页面加载
    await aiAssert('页面显示了文本框表单');

    // 测试输入功能
    await aiInput('测试用户', '全名');
    await aiInput('<EMAIL>', '邮箱');

    // 测试字符串提取
    const pageTitle = await aiString('页面的主标题是什么？');
    console.log('页面标题:', pageTitle);

    // 测试布尔判断
    const hasSubmitButton = await aiBoolean('页面是否有提交按钮？');
    console.log('是否有提交按钮:', hasSubmitButton);

    // 基本验证
    expect(pageTitle).toBeTruthy();
    expect(hasSubmitButton).toBe(true);
  });

  test('验证页面交互功能', async ({ 
    page,
    aiInput,
    aiTap,
    aiAssert
  }) => {
    await page.goto('https://demoqa.com/text-box');
    await page.waitForLoadState('networkidle');

    // 填写完整表单
    await aiInput('张三', '全名');
    await aiInput('<EMAIL>', '邮箱');
    await aiInput('北京市朝阳区', '当前地址');
    await aiInput('上海市浦东新区', '永久地址');

    // 提交表单
    await aiTap('提交');

    // 验证提交结果（等待一下让结果显示）
    await page.waitForTimeout(1000);
    
    // 检查是否有输出结果
    const hasOutput = await page.locator('#output').isVisible();
    expect(hasOutput).toBe(true);
  });
}); 
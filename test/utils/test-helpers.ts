/**
 * 测试辅助工具函数
 */

/**
 * 等待指定时间（毫秒）
 * @param ms 等待时间（毫秒）
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 生成随机字符串
 * @param length 字符串长度
 */
export const generateRandomString = (length: number = 8): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 生成随机邮箱
 */
export const generateRandomEmail = (): string => {
  return `test_${generateRandomString(6)}@example.com`;
};

/**
 * 格式化货币
 * @param amount 金额
 * @param currency 货币符号
 */
export const formatCurrency = (amount: number, currency: string = '$'): string => {
  return `${currency}${amount.toFixed(2)}`;
};

/**
 * 从文本中提取数字
 * @param text 包含数字的文本
 */
export const extractNumber = (text: string): number | null => {
  const match = text.match(/[\d,]+\.?\d*/);
  if (match) {
    return parseFloat(match[0].replace(/,/g, ''));
  }
  return null;
};

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 获取当前时间戳
 */
export const getCurrentTimestamp = (): string => {
  return new Date().toISOString();
};

/**
 * 截取字符串
 * @param str 原字符串
 * @param maxLength 最大长度
 */
export const truncateString = (str: string, maxLength: number = 50): string => {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '...';
}; 
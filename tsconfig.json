{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "commonjs", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["node", "@playwright/test"]}, "include": ["test/**/*", "src/**/*", "playwright.config.ts"], "exclude": ["node_modules", "test-results"]}